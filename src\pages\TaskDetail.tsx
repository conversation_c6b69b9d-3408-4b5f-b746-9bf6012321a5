import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, CheckSquare, Calendar, User, Clock, Tag, MoreVertical, Flag } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTask, useDeleteTask, useCompleteTask, useProject } from '@/hooks/useData';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import TaskForm from '@/components/Tasks/TaskForm';

const TaskDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t, isRTL } = useLanguage();
  const { priority } = useColors();
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const { data: task, isLoading, error } = useTask(id!);
  const { data: project } = useProject(task?.projectId || '');
  const deleteTaskMutation = useDeleteTask();
  const completeTaskMutation = useCompleteTask();

  const handleDelete = async () => {
    if (!task) return;
    try {
      await deleteTaskMutation.mutateAsync(task.id);
      navigate('/', { state: { activeSection: 'tasks' } });
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting task:', error);
    }
  };

  const openDeleteDialog = () => {
    setDeleteDialogOpen(true);
  };

  const handleToggleComplete = async () => {
    if (!task) return;
    try {
      await completeTaskMutation.mutateAsync(task.id);
    } catch (error) {
      console.error('Error toggling task completion:', error);
    }
  };

  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'inProgress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'todo': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getPriorityColor = (taskPriority: string) => {
    switch (taskPriority) {
      case 'high': return priority.high.className;
      case 'medium': return priority.medium.className;
      case 'low': return priority.low.className;
      default: return priority.medium.className;
    }
  };

  const isCompleted = task?.status === 'completed';
  const isOverdue = task?.dueDate && new Date(task.dueDate) < new Date() && !isCompleted;

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
        <p className="text-muted-foreground">{t('common.loading')}</p>
      </div>
    );
  }

  if (error || !task) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <CheckSquare className="w-16 h-16 text-muted-foreground mb-4" />
        <h2 className="text-2xl font-semibold mb-2">{t('tasks.notFound')}</h2>
        <p className="text-muted-foreground mb-4">{t('tasks.notFoundDescription')}</p>
        <Button onClick={() => navigate('/', { state: { activeSection: 'tasks' } })}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          {t('common.back')}
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className={cn("flex items-center justify-between mb-6", isRTL && "flex-row-reverse")}>
          <div className={cn("flex items-center gap-4", isRTL && "flex-row-reverse")}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/', { state: { activeSection: 'tasks' } })}
              className="p-2"
            >
              <ArrowLeft className={cn("w-5 h-5", isRTL && "rotate-180")} />
            </Button>
            <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
              <CheckSquare className="w-6 h-6 text-primary" />
              <div>
                <div className={cn("flex items-center gap-2 text-sm text-muted-foreground mb-1", isRTL && "flex-row-reverse")}>
                  <span className="hover:text-primary cursor-pointer" onClick={() => navigate('/', { state: { activeSection: 'tasks' } })}>
                    {t('tasks.title')}
                  </span>
                  <span>/</span>
                  <span>{task.title}</span>
                </div>
                <h1 className="text-2xl font-bold">{t('tasks.taskDetails')}</h1>
              </div>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="w-5 h-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align={isRTL ? "start" : "end"}>
              <DropdownMenuItem onClick={handleToggleComplete}>
                <CheckSquare className="w-4 h-4 mr-2" />
                {isCompleted ? t('tasks.markIncomplete') : t('tasks.markComplete')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setIsEditFormOpen(true)}>
                <Edit className="w-4 h-4 mr-2" />
                {t('tasks.edit')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={openDeleteDialog}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {t('tasks.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Task Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className={cn("flex items-start justify-between", isRTL && "flex-row-reverse")}>
                  <div className="flex-1">
                    <div className={cn("flex items-center gap-3 mb-2", isRTL && "flex-row-reverse")}>
                      <button
                        onClick={handleToggleComplete}
                        className={cn(
                          "w-6 h-6 rounded border-2 transition-colors flex items-center justify-center",
                          isCompleted
                            ? "bg-green-500 border-green-500 text-white"
                            : "border-muted-foreground hover:border-green-400"
                        )}
                      >
                        {isCompleted && <CheckSquare className="w-4 h-4" />}
                      </button>
                      <CardTitle className={cn(
                        "text-2xl",
                        isCompleted && "line-through text-muted-foreground"
                      )}>
                        {task.title}
                      </CardTitle>
                    </div>
                    {task.description && (
                      <p className="text-muted-foreground whitespace-pre-wrap">{task.description}</p>
                    )}
                  </div>
                  <div className="flex flex-col gap-2">
                    <Badge className={cn("text-sm", getStatusColor(task.status))}>
                      {t(`tasks.status.${task.status}`)}
                    </Badge>
                    <Badge className={cn("text-sm", getPriorityColor(task.priority))}>
                      <Flag className="w-3 h-3 mr-1" />
                      {t(`tasks.priority.${task.priority}`)}
                    </Badge>
                    {isOverdue && (
                      <Badge variant="destructive" className="text-sm">
                        {t('tasks.overdue')}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Tags */}
                {task.tags.length > 0 && (
                  <div>
                    <div className={cn("flex items-center gap-2 mb-3", isRTL && "flex-row-reverse")}>
                      <Tag className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">{t('tasks.tags')}</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {task.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-sm">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Project Link */}
                {project && (
                  <div>
                    <div className={cn("flex items-center gap-2 mb-3", isRTL && "flex-row-reverse")}>
                      <span className="text-sm font-medium text-muted-foreground">{t('tasks.project')}</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate(`/projects/${project.id}`)}
                      className="text-sm hover:bg-primary/10"
                    >
                      {project.name}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Side Info */}
          <div className="space-y-6">
            {/* Details Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('tasks.details')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {task.assignedTo && (
                  <div className="flex items-center justify-between">
                    <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                      <User className="w-4 h-4 text-muted-foreground" />
                      <span className="text-muted-foreground">{t('tasks.assignedTo')}</span>
                    </div>
                    <span className="text-sm font-medium">{task.assignedTo}</span>
                  </div>
                )}
                
                {task.dueDate && (
                  <div className="flex items-center justify-between">
                    <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-muted-foreground">{t('tasks.dueDate')}</span>
                    </div>
                    <span className={cn(
                      "text-sm font-medium",
                      isOverdue && "text-destructive"
                    )}>
                      {formatDate(task.dueDate)}
                    </span>
                  </div>
                )}

                {task.estimatedHours && (
                  <div className="flex items-center justify-between">
                    <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span className="text-muted-foreground">{t('tasks.estimatedHours')}</span>
                    </div>
                    <span className="text-sm font-medium">{task.estimatedHours}h</span>
                  </div>
                )}

                {task.actualHours && (
                  <div className="flex items-center justify-between">
                    <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span className="text-muted-foreground">{t('tasks.actualHours')}</span>
                    </div>
                    <span className="text-sm font-medium">{task.actualHours}h</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Dates Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('tasks.dates')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">{t('tasks.created')}</span>
                  <span className="text-sm">{formatDate(task.createdAt)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">{t('tasks.lastModified')}</span>
                  <span className="text-sm">{formatDate(task.updatedAt)}</span>
                </div>
                {task.completedAt && (
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">{t('tasks.completedAt')}</span>
                    <span className="text-sm">{formatDate(task.completedAt)}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Edit Form */}
        <TaskForm
          isOpen={isEditFormOpen}
          onClose={() => setIsEditFormOpen(false)}
          task={task}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{t('tasks.confirmDelete')}</AlertDialogTitle>
              <AlertDialogDescription>
                {t('tasks.confirmDeleteDescription')}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
                {t('common.cancel')}
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {t('tasks.delete')}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default TaskDetail;
